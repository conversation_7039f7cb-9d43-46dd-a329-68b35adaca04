<?php
// This is a template header file for all module pages
// It includes the common HTML structure, CSS, and navigation elements

// Ensure session is started and config is loaded
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if BASE_URL is defined
if (!defined('BASE_URL')) {
    require_once __DIR__ . '/../config/config.php';
}

// Get current module from URL for highlighting active menu item
$current_path = $_SERVER['PHP_SELF'];
$path_parts = explode('/', $current_path);
$current_module = array_search('modules', $path_parts) !== false ? $path_parts[array_search('modules', $path_parts) + 1] : '';
$current_page = basename($current_path, '.php');

// Get username for display
$username = $_SESSION['username'] ?? 'User';
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Euro Spice | <?php echo ucfirst($current_module); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="icon" type="x-icon" href="<?php echo BASE_URL; ?>/assets/images/eurospice-favicon.png">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #F15B31;
        }

        .space {
            height: 54px;
            width: 100%;
        }

        .body-container {
            padding: 10px;
        }

        #sidebar-nav {
            display: flex;
            flex-direction: column;
        }

        #sidebar-nav a {
            text-decoration: none;
            color: white;
            padding: 10px;
            background-color: #F15B31;
            transition: background-color 0.3s ease;
            width: 100%;
        }

        #sidebar-nav a:hover,
        #sidebar-nav a.active {
            background-color: #D14118;
        }

        .offcanvas-body {
            background-color: #F15B31;
            padding: 0;
        }

        .offcanvas-header {
            background-color: #F15B31;
            color: white;
        }

        #navbar-container {
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            padding: 5px;
            border-radius: 15px;
            width: 90%;
        }

        .container-me {
            background-color: #faf2e9;
            width: 100%;
            min-height: 100vh;
            border-radius: 5px;
            padding: 20px;
            overflow-y: auto;
        }

        .bg-primary {
            background-color: #D14118 !important;
        }

        #open-sidebar {
            text-decoration: none;
            color: white;
            font-weight: 800;
            font-size: 2rem;
            margin-left: 10px;
            margin-right: 10px;
        }

        .dropdown {
            margin-right: 10px;
        }

        .module-card {
            transition: transform 0.2s;
            margin-bottom: 20px;
            height: 100%;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .module-card:hover {
            transform: translateY(-5px);
        }

        .stats-card {
            background-color: #D14118;
            color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: none;
        }

        .row-equal {
            display: flex;
            flex-wrap: wrap;
        }

        .row-equal>[class*='col-'] {
            display: flex;
            flex-direction: column;
            margin-bottom: 30px;
        }

        .card {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 1.5rem;
        }

        .card-title {
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .card-text {
            margin-bottom: 1.5rem;
        }

        .list-group {
            margin-top: auto;
        }

        /* Add any additional custom styles needed for specific modules */
        <?php if ($current_module === 'finance'): ?>.finance-specific {
            /* Finance-specific styles */
        }

        <?php elseif ($current_module === 'inventory'): ?>.inventory-specific {
            /* Inventory-specific styles */
        }

        <?php endif; ?>
    </style>
    <!-- Add any additional module-specific CSS or JS in the specific module files -->
</head>

<body>
    <div class="space"></div>
    <section class="body-container">
        <!-- Offcanvas Sidebar -->
        <div class="offcanvas offcanvas-start" tabindex="-1" id="offcanvas" aria-labelledby="offcanvasLabel">
            <img src="<?php echo BASE_URL; ?>/assets/images/eurospice-logo.png" alt="Euro Spice Logo" width="100%">
            <div class="offcanvas-header">
                <h5 class="offcanvas-title" id="offcanvasLabel">Welcome <?php echo htmlspecialchars($username); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body">
                <nav id="sidebar-nav">
                    <a href="<?php echo BASE_URL; ?>/app/modules/admin/admin_dashboard.php" class="<?php echo $current_module === 'admin' && $current_page === 'admin_dashboard' ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a href="<?php echo BASE_URL; ?>/app/modules/user/add_user.php" class="<?php echo $current_module === 'user' && $current_page === 'add_user' ? 'active' : ''; ?>">
                        <i class="fas fa-user-plus me-2"></i>Add User
                    </a>
                    <a href="<?php echo BASE_URL; ?>/app/modules/user/roles_management.php" class="<?php echo $current_module === 'user' && $current_page === 'roles_management' ? 'active' : ''; ?>">
                        <i class="fas fa-user-shield me-2"></i>Manage Roles
                    </a>
                    <a href="<?php echo BASE_URL; ?>/app/modules/user/departments.php" class="<?php echo $current_module === 'user' && $current_page === 'departments' ? 'active' : ''; ?>">
                        <i class="fas fa-building me-2"></i>Departments
                    </a>
                    <a href="<?php echo BASE_URL; ?>/app/modules/user/add_driver.php" class="<?php echo $current_module === 'user' && $current_page === 'add_driver' ? 'active' : ''; ?>">
                        <i class="fas fa-truck-moving me-2"></i>Manage Drivers
                    </a>
                    <a href="<?php echo BASE_URL; ?>/app/modules/finance/financial_reports.php" class="<?php echo $current_module === 'finance' ? 'active' : ''; ?>">
                        <i class="fas fa-file-invoice-dollar me-2"></i>Finance
                    </a>
                    <a href="<?php echo BASE_URL; ?>/app/modules/inventory/inventory.php" class="<?php echo $current_module === 'inventory' ? 'active' : ''; ?>">
                        <i class="fas fa-warehouse me-2"></i>Inventory
                    </a>
                    <a href="<?php echo BASE_URL; ?>/app/modules/logistics/dashboard_logistics.php" class="<?php echo $current_module === 'logistics' ? 'active' : ''; ?>">
                        <i class="fas fa-truck me-2"></i>Logistics
                    </a>
                    <a href="<?php echo BASE_URL; ?>/app/modules/procurement/procurement.php?action=list" class="<?php echo $current_module === 'procurement' ? 'active' : ''; ?>">
                        <i class="fas fa-shopping-cart me-2"></i>Procurement
                    </a>
                    <a href="<?php echo BASE_URL; ?>/app/modules/reports/reports.php?type=all" class="<?php echo $current_module === 'reports' ? 'active' : ''; ?>">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </a>
                    <a href="<?php echo BASE_URL; ?>/ecommerce_complete/pages/account/login.php?force=1">
                        <i class="fas fa-cash-register me-2"></i>POS Login
                    </a>
                </nav>
            </div>
        </div>

        <div class="navbar navbar-dark bg-primary" id="navbar-container">
            <a class="sidebar-emoji" id="open-sidebar" data-bs-toggle="offcanvas" href="#offcanvas" role="button" aria-controls="offcanvas">
                ☰
            </a>
            <span class="text-white"><?php echo ucfirst($current_module); ?> Module</span>
            <div class="dropdown" id="user-settings">
                <a class="dropdown-toggle text-white text-decoration-none" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    👤
                </a>
                <ul class="dropdown-menu" id="user-dropdown" aria-labelledby="userDropdown">
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/app/modules/user/profile.php"><i class="fas fa-user-edit me-2"></i>Profile</a></li>
                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/app/modules/user/profile.php?view=security"><i class="fas fa-lock me-2"></i>Settings</a></li>
                    <li><a class="dropdown-item" href="#" onclick="logout(); return false;"><i class="fas fa-sign-out-alt me-2"></i>Log Out</a></li>
                </ul>
            </div>
        </div>