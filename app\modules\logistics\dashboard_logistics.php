<?php
require_once '../../config/config.php';
// Get total drivers
$total_drivers = $conn->query("SELECT COUNT(*) FROM drivers")->fetchColumn();
// Get total active deliveries
$total_active = $conn->query("SELECT COUNT(*) FROM order_requests WHERE status = 'in_transit'")->fetchColumn();
// Get total completed deliveries
$total_completed = $conn->query("SELECT COUNT(*) FROM order_requests WHERE status = 'delivered'")->fetchColumn();
?>

<body style="background-color: #faf2e9;">
    <div class="container mt-4">
        <h2>Logistics Dashboard</h2>
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Total Drivers</h5>
                        <h2><?php echo $total_drivers; ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Active Deliveries</h5>
                        <h2><?php echo $total_active; ?></h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <h5 class="card-title">Completed Deliveries</h5>
                        <h2><?php echo $total_completed; ?></h2>
                    </div>
                </div>
            </div>
        </div>
        <a href="../admin/admin_dashboard.php" class="btn btn-secondary mt-3" style="background-color: #f15b31; color: white;">Back to Dashboard</a>
    </div>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <style>
        .stats-card {
            background: #f15b31;
            color: white;
            margin-bottom: 20px;
            border-radius: 10px;
        }
    </style>
</body>