<?php
require_once '../../config/config.php';

// Handle form submission
$success_message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $pack_name = $_POST['pack_name'];
    $description = $_POST['description'];
    $material = $_POST['material'];
    $pack_logo = '';

    if (isset($_FILES['pack_logo']) && $_FILES['pack_logo']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/packs/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        $file_ext = pathinfo($_FILES['pack_logo']['name'], PATHINFO_EXTENSION);
        $file_name = uniqid('pack_', true) . '.' . $file_ext;
        $target_path = $upload_dir . $file_name;
        if (move_uploaded_file($_FILES['pack_logo']['tmp_name'], $target_path)) {
            $pack_logo = $target_path;
        }
    }

    // Check if the table exists, if not create it
    $check_table = $conn->query("SHOW TABLES LIKE 'pack_types'");
    if ($check_table->rowCount() == 0) {
        $create_table = "CREATE TABLE pack_types (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            pack_name VARCHAR(100) NOT NULL,
            description TEXT,
            material VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->exec($create_table);
    }

    $stmt = $conn->prepare("INSERT INTO pack_types (pack_name, description, material, pack_logo) VALUES (?, ?, ?, ?)");
    $stmt->execute([$pack_name, $description, $material, $pack_logo]);
    $success_message = 'Package type added successfully!';
}

// Fetch all pack types
$packs = [];
$check_table = $conn->query("SHOW TABLES LIKE 'pack_types'");
if ($check_table->rowCount() > 0) {
    $sql = "SELECT * FROM pack_types ORDER BY id DESC";
    $packs_result = $conn->query($sql);
    if ($packs_result) {
        $packs = $packs_result->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Package Types</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Procurement Management</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="spices.php">Manage Spices</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="brands.php">Manage Brands</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.php">Manage Suppliers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="measurement_units.php">Manage Units</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="pack_types.php">Manage Pack Types</a>
                    </li>
                </ul>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="procurement.php">Orders</a>
                    <a class="nav-link" href="logout.php">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h2>Manage Package Types</h2>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Add New Package Type</h5>
                        <form method="post" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="pack_name" class="form-label">Package Name</label>
                                <input type="text" class="form-control" id="pack_name" name="pack_name" required>
                            </div>
                            <div class="mb-3">
                                <label for="material" class="form-label">Material</label>
                                <input type="text" class="form-control" id="material" name="material" required>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Add Package Type</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <h3>Package Types List</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Package Name</th>
                                <th>Material</th>
                                <th>Description</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($packs as $pack): ?>
                                <tr>
                                    <td><?php echo $pack['id']; ?></td>
                                    <td><?php echo htmlspecialchars($pack['pack_name']); ?></td>
                                    <td><?php echo htmlspecialchars($pack['material']); ?></td>
                                    <td><?php echo htmlspecialchars($pack['description']); ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">Edit</button>
                                        <button class="btn btn-sm btn-danger">Delete</button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
</body>

</html>