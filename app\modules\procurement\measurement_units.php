<?php
require_once '../../config/config.php';

// Handle form submission
$success_message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $unit_name = $_POST['unit_name'];
    $unit_symbol = $_POST['unit_symbol'];
    $description = $_POST['description'];
    $unit_logo = '';

    if (isset($_FILES['unit_logo']) && $_FILES['unit_logo']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = 'uploads/units/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        $file_ext = pathinfo($_FILES['unit_logo']['name'], PATHINFO_EXTENSION);
        $file_name = uniqid('unit_', true) . '.' . $file_ext;
        $target_path = $upload_dir . $file_name;
        if (move_uploaded_file($_FILES['unit_logo']['tmp_name'], $target_path)) {
            $unit_logo = $target_path;
        }
    }

    // Check if the table exists, if not create it
    $check_table = $conn->query("SHOW TABLES LIKE 'measurement_units'");
    if ($check_table->rowCount() == 0) {
        $create_table = "CREATE TABLE measurement_units (
            id INT(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
            unit_name VARCHAR(100) NOT NULL,
            unit_symbol VARCHAR(20) NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->exec($create_table);
    }

    $stmt = $conn->prepare("INSERT INTO measurement_units (unit_name, unit_symbol, description, unit_logo) VALUES (?, ?, ?, ?)");
    $stmt->execute([$unit_name, $unit_symbol, $description, $unit_logo]);
    $success_message = 'Measurement unit added successfully!';
}

// Fetch all measurement units
$units = [];
$check_table = $conn->query("SHOW TABLES LIKE 'measurement_units'");
if ($check_table->rowCount() > 0) {
    $sql = "SELECT * FROM measurement_units ORDER BY id DESC";
    $units_result = $conn->query($sql);
    if ($units_result) {
        $units = $units_result->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Measurement Units</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
</head>

<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">Procurement Management</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="spices.php">Manage Spices</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="brands.php">Manage Brands</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="suppliers.php">Manage Suppliers</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="measurement_units.php">Manage Units</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pack_types.php">Manage Pack Types</a>
                    </li>
                </ul>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="procurement.php">Orders</a>
                    <a class="nav-link" href="logout.php">Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h2>Manage Measurement Units</h2>

        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo $success_message; ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Add New Measurement Unit</h5>
                        <form method="post" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="unit_name" class="form-label">Unit Name</label>
                                <input type="text" class="form-control" id="unit_name" name="unit_name" required>
                            </div>
                            <div class="mb-3">
                                <label for="unit_symbol" class="form-label">Unit Symbol</label>
                                <input type="text" class="form-control" id="unit_symbol" name="unit_symbol" required>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Add Unit</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <h3>Measurement Units List</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Unit Name</th>
                                <th>Symbol</th>
                                <th>Description</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($units as $unit): ?>
                                <tr>
                                    <td><?php echo $unit['id']; ?></td>
                                    <td><?php echo htmlspecialchars($unit['unit_name']); ?></td>
                                    <td><?php echo htmlspecialchars($unit['unit_symbol']); ?></td>
                                    <td><?php echo htmlspecialchars($unit['description']); ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">Edit</button>
                                        <button class="btn btn-sm btn-danger">Delete</button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
</body>

</html>