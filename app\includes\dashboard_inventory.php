<?php

/**
 * Dashboard Inventory Component
 * This file fetches data from the inventory and displays it in the dashboard
 */

// Make sure we have a database connection
if (!isset($conn)) {
    require_once __DIR__ . '/../config/config.php';
}

// No need for use statements here as PDO and PDOException are in global namespace

// Function to get total product count
function getTotalProductCount($conn)
{
    try {
        $query = "SELECT COUNT(*) as total FROM products";
        $stmt = $conn->query($query);
        if ($stmt) {
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['total'] ?? 0;
        }
        return 0;
    } catch (PDOException $e) {
        error_log("Error in getTotalProductCount: " . $e->getMessage());
        return 0;
    }
}

// Function to get low stock products (less than 10 items)
function getLowStockProducts($conn, $threshold = 10)
{
    try {
        // Check if stock column exists
        $check_column = $conn->query("SHOW COLUMNS FROM products LIKE 'stock'");
        if ($check_column->rowCount() > 0) {
            $query = "SELECT COUNT(*) as count FROM products WHERE stock < :threshold AND stock > 0";
            $stmt = $conn->prepare($query);
            $stmt->execute(['threshold' => $threshold]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] ?? 0;
        }
        return 0;
    } catch (PDOException $e) {
        error_log("Error in getLowStockProducts: " . $e->getMessage());
        return 0;
    }
}

// Function to get out of stock products
function getOutOfStockProducts($conn)
{
    try {
        // Check if stock column exists
        $check_column = $conn->query("SHOW COLUMNS FROM products LIKE 'stock'");
        if ($check_column->rowCount() > 0) {
            $query = "SELECT COUNT(*) as count FROM products WHERE stock = 0";
            $stmt = $conn->query($query);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] ?? 0;
        }
        return 0;
    } catch (PDOException $e) {
        error_log("Error in getOutOfStockProducts: " . $e->getMessage());
        return 0;
    }
}

// Function to get total inventory value
function getTotalInventoryValue($conn)
{
    try {
        // Check if price and stock columns exist
        $check_price = $conn->query("SHOW COLUMNS FROM products LIKE 'price'");
        $check_stock = $conn->query("SHOW COLUMNS FROM products LIKE 'stock'");

        if ($check_price->rowCount() > 0 && $check_stock->rowCount() > 0) {
            $query = "SELECT SUM(price * stock) as total_value FROM products";
            $stmt = $conn->query($query);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['total_value'] ?? 0;
        }
        return 0;
    } catch (PDOException $e) {
        error_log("Error in getTotalInventoryValue: " . $e->getMessage());
        return 0;
    }
}

// Function to get products by category/brand
function getProductsByCategory($conn)
{
    try {
        // First check if the brand column exists
        $check_column = $conn->query("SHOW COLUMNS FROM products LIKE 'brand'");
        if ($check_column->rowCount() > 0) {
            $query = "SELECT brand, COUNT(*) as count FROM products GROUP BY brand ORDER BY count DESC LIMIT 5";
            $stmt = $conn->query($query);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            // If brand column doesn't exist, return empty array
            return [];
        }
    } catch (PDOException $e) {
        // Log error and return empty array
        error_log("Error in getProductsByCategory: " . $e->getMessage());
        return [];
    }
}

// Function to get recently added products
function getRecentProducts($conn, $limit = 5)
{
    try {
        // Check if products table exists
        $check_table = $conn->query("SHOW TABLES LIKE 'products'");
        if ($check_table->rowCount() > 0) {
            $query = "SELECT * FROM products ORDER BY id DESC LIMIT :limit";
            $stmt = $conn->prepare($query);
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        return [];
    } catch (PDOException $e) {
        error_log("Error in getRecentProducts: " . $e->getMessage());
        return [];
    }
}

// Function to get expiring products (within 30 days)
function getExpiringProducts($conn, $days = 30)
{
    try {
        // Check if expirationDate column exists
        $check_column = $conn->query("SHOW COLUMNS FROM products LIKE 'expirationDate'");
        if ($check_column->rowCount() > 0) {
            $today = date('Y-m-d');
            $expiry_date = date('Y-m-d', strtotime("+{$days} days"));

            $query = "SELECT * FROM products
                    WHERE expirationDate BETWEEN :today AND :expiry_date
                    ORDER BY expirationDate ASC
                    LIMIT 10";

            $stmt = $conn->prepare($query);
            $stmt->execute([
                'today' => $today,
                'expiry_date' => $expiry_date
            ]);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        return [];
    } catch (PDOException $e) {
        error_log("Error in getExpiringProducts: " . $e->getMessage());
        return [];
    }
}

// Get all the data
$total_products = getTotalProductCount($conn);
$low_stock_products = getLowStockProducts($conn);
$out_of_stock_products = getOutOfStockProducts($conn);
$total_inventory_value = getTotalInventoryValue($conn);
$products_by_category = getProductsByCategory($conn);
$recent_products = getRecentProducts($conn);
$expiring_products = getExpiringProducts($conn);
?>

<div class="row mb-4">
    <!-- Total Products Widget -->
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <h5 class="card-title">Total Products</h5>
                <h2 class="card-text"><?php echo $total_products; ?></h2>
            </div>
        </div>
    </div>

    <!-- Low Stock Widget -->
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <h5 class="card-title">Low Stock</h5>
                <h2 class="card-text"><?php echo $low_stock_products; ?></h2>
            </div>
        </div>
    </div>

    <!-- Out of Stock Widget -->
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <h5 class="card-title">Out of Stock</h5>
                <h2 class="card-text"><?php echo $out_of_stock_products; ?></h2>
            </div>
        </div>
    </div>

    <!-- Total Inventory Value Widget -->
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <h5 class="card-title">Total Value</h5>
                <h2 class="card-text">₱<?php echo number_format($total_inventory_value, 2); ?></h2>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- Products by Brand Chart -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                Products by Brand
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Brand</th>
                            <th>Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($products_by_category as $category): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($category['brand'] ?? ''); ?></td>
                                <td><?php echo $category['count'] ?? 0; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Recent Products -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                Recently Added Products
            </div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Brand</th>
                            <th>Price</th>
                            <th>Stock</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_products as $product): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($product['name'] ?? ''); ?></td>
                                <td><?php echo htmlspecialchars($product['brand'] ?? ''); ?></td>
                                <td>₱<?php echo number_format($product['price'] ?? 0, 2); ?></td>
                                <td><?php echo $product['stock'] ?? 0; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Expiring Products -->
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                Products Expiring Soon (Next 30 Days)
            </div>
            <div class="card-body">
                <?php if (count($expiring_products) > 0): ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Brand</th>
                                <th>Expiration Date</th>
                                <th>Stock</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($expiring_products as $product): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($product['name'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($product['brand'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($product['expirationDate'] ?? ''); ?></td>
                                    <td><?php echo $product['stock'] ?? 0; ?></td>
                                    <td>
                                        <a href="../inventory/inventory.php" class="btn btn-sm btn-primary">View</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <p class="text-center">No products expiring soon.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Add a link to the full inventory -->
<div class="row mt-4">
    <div class="col-12 text-center">
        <a href="../inventory/inventory.php" class="btn btn-primary">View Full Inventory</a>
    </div>
</div>