<?php
// Include database connection
require_once '../../config/config.php';
/** @var PDO $conn */
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="x-icon" href="upload/eurospice-favicon.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-SgOJa3DmI69IUzQ2PVdRZhwQ+dy64/BUtbMJw1MZ8t5HZApcHrRKUc4W0kG879m7" crossorigin="anonymous">
    <link
        href="https://cdn.prod.website-files.com/66a833f537135b05bc1eaecb/css/maria-bettinas-dynamite-site.webflow.05b59e178.css"
        rel="stylesheet" type="text/css">
    <title>Inventory</title>
</head>

<style>
    * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-family: 'Poppins', sans-serif;
    }

    body {
        background-image: url('../assets/images/eurospice-grid.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-attachment: fixed;
        height: 700px;
    }

    body,
    html {
        margin: 0;
        padding: 0;
        overflow-x: hidden;
    }

    .navbar-container h3 {
        font-size: 1rem;
        text-align: center;
        font-family: 'Poppins', sans-serif;
        padding: 10px;
        color: white;
    }

    .navbar-container h3 a {
        text-decoration: underline;
    }

    .navbar {
        background-color: #b82720;
    }

    .inventorytable {
        width: 100%;
        height: 100%;
        display: grid;
        place-items: center;
    }

    .action-buttons {
        margin: 20px 0;
    }

    .log-out-btn {
        color: white;
        text-decoration: none;
        padding: 5px 10px;
    }

    .log-out-btn:hover {
        color: #ffc107;
    }

    .add-to-cart-btn {
        background-color: #b82720;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 5px 10px;
        cursor: pointer;
    }

    .add-to-cart-btn:hover {
        background-color: #951f19;
    }

    .modal-header {
        background-color: #b82720;
        color: white;
    }

    .cart-quantity {
        width: 70px;
    }

    .table-responsive {
        overflow-x: auto;
    }

    #currentdatetime {
        text-align: right;
        margin-bottom: 15px;
        font-style: italic;
    }

    /* Title styling with orange background */
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        background-color: #f15b31;
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        display: inline-block;
        min-width: 200px;
        text-align: center;
    }
</style>

<body>
    <nav class="navbar navbar-expand-lg bg-body-transparent">
        <div class="container-fluid">

            <a class="navbar-brand text-white" href="#"><img src="assets/images/eurospice-logo.png"
                    alt=""></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false"
                aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarSupportedContent">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link active text-white" aria-current="page" href="../product/supplier_prod.php">Supplier Table</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" aria-current="page" href="../finance/PAR.php">Confirmation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" aria-current="page" href="../inventory/inventory.php">Inventory</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" aria-current="page" href="../inventory/inventory_dashboard.php">Dashboard</a>
                    </li>
                </ul>

                <div class="user-container d-flex me-auto mb-2 mb-lg-0">
                    <a class="log-out-btn" href="../auth/logout.php">Log out</a>
                </div>

                <div class="user-container d-flex me-auto mb-2 mb-lg-0">
                    <a class="log-out-btn" href="../inventory/cart.php">🛒 <span id="cart-count" class="badge bg-warning">0</span></a>
                </div>

            </div>
        </div>
    </nav>

    <div class="main_content">
        <div id="currentdatetime"></div>
        <a href="../admin/admin_dashboard.php" class="btn mt-3" style="background-color: #f15b31; color: white;">Back to Dashboard</a>

        <div class="inventorytable">
            <h2>Inventory Table</h2>
            <table class="table">
                <thead>
                    <tr>
                        <th>Product Image</th>
                        <th>Product Name</th>
                        <th>Brand</th>
                        <th>Price</th>
                        <th>Product Measurement</th>
                        <th>Package Type</th>
                        <th>Expiration Date</th>
                        <th>Date Delivered</th>
                        <th>Country</th>
                        <th>Batch Code</th>
                        <th>Stocks</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="productTableBody">
                    <!-- Products will be inserted here dynamically -->
                    <?php
                    // Query to get all products from inventory table
                    $query = "SELECT * FROM inventory ORDER BY id DESC";
                    $stmt = $conn->query($query);
                    $products = $stmt ? $stmt->fetchAll(PDO::FETCH_ASSOC) : [];

                    if (count($products) > 0) {
                        foreach ($products as $row) {
                            // Map inventory table fields to the expected format for the modal
                            // Use "Season" as the default product name if it's empty
                            $productName = !empty($row['prod_name']) ? $row['prod_name'] : 'Season';

                            $modalData = [
                                'id' => $row['id'],
                                'image' => $row['prod_image'] ?? '',
                                'name' => $productName,
                                'brand' => $row['brand_name'] ?? '',
                                'price' => $row['price'] ?? '',
                                'weight' => $row['prod_measure'] ?? '',
                                'packtype' => $row['pack_type'] ?? '',
                                'expirationDate' => $row['expiry_date'] ?? '',
                                'delivered' => $row['delivered_date'] ?? '',
                                'country' => $row['country'] ?? '',
                                'batchCode' => $row['batch_code'] ?? '',
                                'stock' => $row['stocks'] ?? 0
                            ];

                            echo "<tr>";
                            echo "<td><img src='../finance/uploads/" . htmlspecialchars($row['prod_image'] ?? '') . "' width='100' height='100'></td>";
                            // Display "Season" if prod_name is empty
                            $displayName = !empty($row['prod_name']) ? $row['prod_name'] : 'Season';
                            echo "<td>" . htmlspecialchars($displayName) . "</td>";
                            echo "<td>" . htmlspecialchars($row['brand_name'] ?? '') . "</td>";
                            echo "<td>₱" . htmlspecialchars($row['price'] ?? '') . "</td>";
                            echo "<td>" . htmlspecialchars($row['prod_measure'] ?? '') . "</td>";
                            echo "<td>" . htmlspecialchars($row['pack_type'] ?? '') . "</td>";
                            echo "<td>" . htmlspecialchars($row['expiry_date'] ?? '') . "</td>";
                            echo "<td>" . htmlspecialchars($row['delivered_date'] ?? '') . "</td>";
                            echo "<td>" . htmlspecialchars($row['country'] ?? '') . "</td>";
                            echo "<td>" . htmlspecialchars($row['batch_code'] ?? '') . "</td>";
                            echo "<td>" . htmlspecialchars($row['stocks'] ?? '') . "</td>";
                            echo "<td>" . (($row['stocks'] ?? 0) > 0 ? 'Available' : 'Out of Stock') . "</td>";
                            echo "<td>
                                <button class='add-to-cart-btn' onclick='openAddToCartModal(" . json_encode($modalData) . ")'>Add to Cart</button>
                        </td>";
                            echo "</tr>";
                        }
                    } else {
                        echo "<tr><td colspan='13' class='text-center'>No products found in inventory</td></tr>";
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Add to Cart Modal -->
    <div class="modal fade" id="addToCartModal" tabindex="-1" aria-labelledby="addToCartModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addToCartModalLabel">Add to Cart</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <img id="modalProductImage" src="" alt="Product Image" class="img-fluid">
                        </div>
                        <div class="col-md-8">
                            <h4 id="modalProductName"></h4>
                            <p><strong>Brand:</strong> <span id="modalBrand"></span></p>
                            <p><strong>Price:</strong> ₱<span id="modalPrice"></span></p>
                            <p><strong>Measurement:</strong> <span id="modalMeasurement"></span></p>
                            <p><strong>Package Type:</strong> <span id="modalPackageType"></span></p>
                            <p><strong>Expiration Date:</strong> <span id="modalExpirationDate"></span></p>
                            <p><strong>Country:</strong> <span id="modalCountry"></span></p>
                            <p><strong>Batch Code:</strong> <span id="modalBatchCode"></span></p>
                            <p><strong>Available Stocks:</strong> <span id="modalStocks"></span></p>

                            <div class="mb-3">
                                <label for="cartQuantity" class="form-label">Quantity:</label>
                                <input type="number" class="form-control cart-quantity" id="cartQuantity" min="1" value="1">
                                <div id="quantityError" class="text-danger"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" style="background-color: #b82720; border-color: #b82720;" id="confirmAddToCart">Add to Cart</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
    <script src="../inventory/handle_missing_images.js"></script>

    <script>
        let currentProduct = null;

        // Function to open the Add to Cart modal
        function openAddToCartModal(product) {
            currentProduct = product;

            // Set product details in the modal
            document.getElementById('modalProductImage').src = '../finance/uploads/' + product.image;
            document.getElementById('modalProductName').textContent = product.name;
            document.getElementById('modalBrand').textContent = product.brand;
            document.getElementById('modalPrice').textContent = product.price;
            document.getElementById('modalMeasurement').textContent = product.weight;
            document.getElementById('modalPackageType').textContent = product.packtype;
            document.getElementById('modalExpirationDate').textContent = product.expirationDate;
            document.getElementById('modalCountry').textContent = product.country;
            document.getElementById('modalBatchCode').textContent = product.batchCode;
            document.getElementById('modalStocks').textContent = product.stock;

            // Reset quantity to 1 and clear any error messages
            document.getElementById('cartQuantity').value = 1;
            document.getElementById('quantityError').textContent = '';

            // Set max quantity to available stock
            document.getElementById('cartQuantity').max = product.stock;

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('addToCartModal'));
            modal.show();
        }

        // Event listener for the Add to Cart confirmation button
        document.getElementById('confirmAddToCart').addEventListener('click', function() {
            const quantity = parseInt(document.getElementById('cartQuantity').value);
            const availableStock = parseInt(currentProduct.stock);

            // Validate quantity
            if (quantity <= 0) {
                document.getElementById('quantityError').textContent = 'Quantity must be at least 1';
                return;
            }

            if (quantity > availableStock) {
                document.getElementById('quantityError').textContent = 'Quantity cannot exceed available stock';
                return;
            }

            // Add to cart via AJAX
            const formData = new FormData();
            formData.append('product_id', currentProduct.id);
            formData.append('quantity', quantity);
            formData.append('action', 'add');

            fetch('../inventory/cart_controller_fix.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    // Check if the response is valid JSON
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        return response.json();
                    } else {
                        // If not JSON, throw an error that will be caught below
                        throw new Error('Invalid response format. Expected JSON.');
                    }
                })
                .then(data => {
                    if (data.status === 'success') {
                        // Close the modal
                        bootstrap.Modal.getInstance(document.getElementById('addToCartModal')).hide();

                        // Update cart count
                        document.getElementById('cart-count').textContent = data.cart_count;

                        // Show success alert
                        alert('Product added to cart successfully!');
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while adding the product to cart.');
                });
        });

        // Function to display the current date and time
        function updateDateTime() {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            document.getElementById('currentdatetime').textContent = now.toLocaleDateString('en-US', options);
        }

        // Update date and time when page loads
        updateDateTime();

        // Update cart count on page load
        fetch('../inventory/cart_controller_fix.php?action=count')
            .then(response => {
                // Check if the response is valid JSON
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json();
                } else {
                    // If not JSON, throw an error that will be caught below
                    throw new Error('Invalid response format. Expected JSON.');
                }
            })
            .then(data => {
                document.getElementById('cart-count').textContent = data.cart_count;
            })
            .catch(error => {
                console.error('Error:', error);
            });
    </script>
</body>

</html>